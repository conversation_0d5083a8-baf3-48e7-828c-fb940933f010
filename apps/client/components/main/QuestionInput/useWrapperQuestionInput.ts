import { courseTimer } from "~/composables/courses/courseTimer";
import { useGameMode } from "~/composables/main/game";
import { useInput } from "~/composables/main/question";
import { useSummary } from "~/composables/main/summary";
import { useAutoNextQuestion } from "~/composables/user/autoNext";
import { useKeyboardSound } from "~/composables/user/sound";
import { useSpaceSubmitAnswer } from "~/composables/user/submitKey";
import { useCourseStore } from "~/store/course";
import { useQuestionInput } from "./questionInputHelper";
import { useAnswerError } from "./useAnswerError";
import { usePlayTipSound, useTypingSound } from "./useTypingSound";

// 智能分割日语文本的辅助函数
function smartSplitJapanese(text: string, targetCount: number): string[] {
  // 常见的日语助词和语法标记
  const particles = [
    "は",
    "が",
    "を",
    "に",
    "で",
    "と",
    "から",
    "まで",
    "より",
    "について",
    "です",
    "だ",
    "である",
    "ます",
    "か",
  ];

  // 尝试基于助词分割
  const result: string[] = [];
  let remaining = text;

  for (let i = 0; i < targetCount - 1 && remaining.length > 0; i++) {
    let found = false;

    // 寻找助词位置
    for (const particle of particles) {
      const index = remaining.indexOf(particle);
      if (index > 0) {
        const part = remaining.substring(0, index + particle.length);
        result.push(part);
        remaining = remaining.substring(index + particle.length);
        found = true;
        break;
      }
    }

    // 如果没找到助词，按比例分割
    if (!found) {
      const partLength = Math.ceil(remaining.length / (targetCount - i));
      const part = remaining.substring(0, partLength);
      result.push(part);
      remaining = remaining.substring(partLength);
    }
  }

  // 添加剩余部分
  if (remaining.length > 0) {
    result.push(remaining);
  }

  return result;
}

// 智能分割日语文本，基于罗马音的单词数量
function splitJapaneseByRomaji(japanese: string, romajiWords: string[]): string[] {
  // 1. 如果日语包含空格，按空格分割
  if (japanese.includes(" ")) {
    return japanese.split(" ");
  }

  // 2. 预定义的常见分割模式
  const knownSplits: Record<string, string[]> = {
    私は: ["私", "は"],
    私は小明です: ["私", "は", "小明", "です"],
    私は相手と友達です: ["私", "は", "相手", "と", "友達", "です"],
    相手はとても優しいです: ["相手", "は", "とても", "優しい", "です"],
    私は相手と話す必要があります: ["私", "は", "相手", "と", "話す", "必要", "が", "あります"],
    相手についてどう思いますか: ["相手", "について", "どう", "思います", "か"],
    私たちは相互に信頼できる相手です: [
      "私たち",
      "は",
      "相互",
      "に",
      "信頼",
      "できる",
      "相手",
      "です",
    ],
    どういたしまして: ["どういたしまして"], // 作为整体
    こんにちは: ["こんにちは"], // 作为整体
    ありがとう: ["ありがとう"], // 作为整体
    すみません: ["すみません"], // 作为整体
    おはよう: ["おはよう"], // 作为整体
  };

  // 3. 检查是否有预定义的分割
  if (knownSplits[japanese]) {
    return knownSplits[japanese];
  }

  // 4. 基于罗马音单词数量的智能分割
  if (romajiWords.length === 2) {
    // 常见的助词模式
    const particlePattern = /^(.+)(は|が|を|に|で|と|から|まで|より|です)$/;
    const match = japanese.match(particlePattern);
    if (match) {
      return [match[1], match[2]];
    }

    // 默认：按字符数平均分割（作为最后手段）
    const midPoint = Math.ceil(japanese.length / 2);
    return [japanese.substring(0, midPoint), japanese.substring(midPoint)];
  }

  if (romajiWords.length >= 3) {
    // 尝试更智能的分割方式
    return smartSplitJapanese(japanese, romajiWords.length);
  }

  // 5. 其他情况：返回整个日语文本作为单个单词
  return [japanese];
}

// 智能分割平假名文本，基于罗马音的单词数量
function splitHiraganaByRomaji(hiragana: string, romajiWords: string[]): string[] {
  // 1. 如果平假名包含空格，按空格分割
  if (hiragana.includes(" ")) {
    return hiragana.split(" ");
  }

  // 2. 预定义的常见平假名分割模式
  const knownHiraganaSplits: Record<string, string[]> = {
    わたしは: ["わたし", "は"],
    わたしはしょうめいです: ["わたし", "は", "しょうめい", "です"],
    わたしはあいてとともだちです: ["わたし", "は", "あいて", "と", "ともだち", "です"],
    あいてはとてもやさしいです: ["あいて", "は", "とても", "やさしい", "です"],
    わたしはあいてとはなすひつようがあります: [
      "わたし",
      "は",
      "あいて",
      "と",
      "はなす",
      "ひつよう",
      "が",
      "あります",
    ],
    あいてについてどうおもいますか: ["あいて", "について", "どう", "おもいます", "か"],
    わたしたちはそうごにしんらいできるあいてです: [
      "わたしたち",
      "は",
      "そうご",
      "に",
      "しんらい",
      "できる",
      "あいて",
      "です",
    ],
    どういたしまして: ["どういたしまして"], // 作为整体
    こんにちは: ["こんにちは"], // 作为整体
    ありがとう: ["ありがとう"], // 作为整体
    すみません: ["すみません"], // 作为整体
    おはよう: ["おはよう"], // 作为整体
    おはようございます: ["おはよう", "ございます"],
    こんばんは: ["こんばんは"], // 作为整体
    さようなら: ["さようなら"], // 作为整体
  };

  // 3. 检查是否有预定义的分割
  if (knownHiraganaSplits[hiragana]) {
    return knownHiraganaSplits[hiragana];
  }

  // 4. 基于罗马音单词数量的智能分割
  if (romajiWords.length === 2) {
    // 常见的助词模式（平假名版本）
    const particlePattern = /^(.+)(は|が|を|に|で|と|から|まで|より|です|だ)$/;
    const match = hiragana.match(particlePattern);
    if (match) {
      return [match[1], match[2]];
    }

    // 默认：按字符数平均分割
    const midPoint = Math.ceil(hiragana.length / 2);
    return [hiragana.substring(0, midPoint), hiragana.substring(midPoint)];
  }

  if (romajiWords.length >= 3) {
    // 尝试更智能的分割方式
    return smartSplitJapanese(hiragana, romajiWords.length);
  }

  // 5. 其他情况：返回整个平假名文本作为单个单词
  return [hiragana];
}

export function useWrapperQuestionInput() {
  const courseStore = useCourseStore();
  const { showAnswer } = useGameMode();
  const { showSummary } = useSummary();
  const { setInputCursorPosition, getInputCursorPosition, blurInput, focusInput } =
    useQuestionInput();
  const { isKeyboardSoundEnabled } = useKeyboardSound();
  const { checkPlayTypingSound, playTypingSound } = useTypingSound();
  const { handleAnswerError } = useAnswerError();
  const { playRightSound } = usePlayTipSound();
  const { isAutoNextQuestion } = useAutoNextQuestion();
  const { isUseSpaceSubmitAnswer } = useSpaceSubmitAnswer();

  const {
    initialize: initializeQuestionInput,
    findWordById,
    inputValue,
    submitAnswer,
    setInputValue,
    handleKeyboardInput,
    isFixMode,
    isFixInputMode,
  } = useInput({
    source: () =>
      courseStore.currentStatement?.romaji || courseStore.currentStatement?.english || "",
    setInputCursorPosition,
    getInputCursorPosition,
    inputChangedCallback,
    getAlternativeAnswers: () => {
      // 提供日语汉字和平假名作为替代答案
      const japanese = courseStore.currentStatement?.japanese || "";
      const hiragana = courseStore.currentStatement?.hiragana || "";
      const romaji =
        courseStore.currentStatement?.romaji || courseStore.currentStatement?.english || "";

      const alternatives: string[] = [];

      // 处理空格：日语通常不包含空格，需要根据罗马音的空格位置来分割
      if (romaji) {
        const romajiWords = romaji.split(" ");

        // 添加日语汉字作为替代答案
        if (japanese) {
          if (romajiWords.length > 1) {
            const japaneseWords = splitJapaneseByRomaji(japanese, romajiWords);
            alternatives.push(...japaneseWords);
            console.log("日语汉字分割结果:", japaneseWords);
          } else {
            alternatives.push(japanese);
          }
        }

        // 添加平假名作为替代答案
        if (hiragana) {
          if (romajiWords.length > 1) {
            const hiraganaWords = splitHiraganaByRomaji(hiragana, romajiWords);
            alternatives.push(...hiraganaWords);
            console.log("平假名分割结果:", hiraganaWords);
          } else {
            alternatives.push(hiragana);
          }
        }
      }

      console.log("所有替代答案:", alternatives);
      return alternatives;
    },
  });

  function inputChangedCallback(e: KeyboardEvent) {
    if (isKeyboardSoundEnabled() && checkPlayTypingSound(e)) {
      playTypingSound();
    }
  }

  function handleAnswerRight() {
    courseTimer.timeEnd(String(courseStore.statementIndex)); // 停止当前题目的计时
    playRightSound();

    if (isAutoNextQuestion()) {
      // 自动下一题
      if (courseStore.isAllDone()) {
        blurInput(); // 失去输入焦点，防止结束时光标仍然在输入框，造成后续结算面板回车事件无法触发
        showSummary();
      }
      courseStore.toNextStatement();
    } else {
      showAnswer();
    }
  }

  return {
    initializeQuestionInput,
    isFixMode,
    isFixInputMode,
    findWordById,
    inputValue,
    setInputValue,
    submitAnswer() {
      submitAnswer(handleAnswerRight, handleAnswerError);
      focusInput();
    },
    handleKeyboardInput(e: KeyboardEvent) {
      handleKeyboardInput(e, {
        useSpaceSubmitAnswer: {
          enable: isUseSpaceSubmitAnswer(),
          rightCallback: handleAnswerRight,
          errorCallback: handleAnswerError,
        },
      });
    },
  };
}
