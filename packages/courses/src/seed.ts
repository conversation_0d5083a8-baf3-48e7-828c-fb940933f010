import fs from "node:fs";
import path from "node:path";

import { db } from "@earthworm/db";
import {
  coursePack,
  course as courseSchema,
  statement as statementSchema,
} from "@earthworm/schema";

type Statement = typeof statementSchema.$inferInsert;
type CoursePackMeta = typeof coursePack.$inferInsert;

interface CoursePackData {
  meta: CoursePackMeta;
  courseFiles: string[];
  dirPath: string;
}

// 扫描数据目录，获取所有课程包信息
function scanCoursePackDirectories(): CoursePackData[] {
  const dataDir = path.resolve(__dirname, "../data");
  const entries = fs.readdirSync(dataDir, { withFileTypes: true });

  const coursePacksData: CoursePackData[] = [];

  for (const entry of entries) {
    if (entry.isDirectory()) {
      const dirPath = path.join(dataDir, entry.name);
      const metaPath = path.join(dirPath, "meta.json");

      // 检查是否存在 meta.json 文件
      if (fs.existsSync(metaPath)) {
        try {
          const metaContent = fs.readFileSync(metaPath, "utf-8");
          const meta = JSON.parse(metaContent) as CoursePackMeta;

          // 获取目录下除 meta.json 外的所有 JSON 文件
          const allFiles = fs.readdirSync(dirPath);
          const courseFiles = allFiles.filter(
            (file) => file.endsWith(".json") && file !== "meta.json",
          );

          coursePacksData.push({
            meta,
            courseFiles,
            dirPath,
          });

          console.log(`发现课程包: ${meta.title} (${courseFiles.length} 个课程文件)`);
        } catch (error) {
          console.error(`读取课程包 ${entry.name} 的 meta.json 失败:`, error);
        }
      }
    }
  }

  // 按照 order 字段排序
  return coursePacksData.sort((a, b) => (a.meta.order || 0) - (b.meta.order || 0));
}

(async function () {
  // 按照外键依赖顺序删除：先删除 statements，再删除 courses，最后删除 coursePack
  await db.delete(statementSchema);
  await db.delete(courseSchema);
  await db.delete(coursePack);

  // 扫描所有课程包目录
  const coursePacksData = scanCoursePackDirectories();

  if (coursePacksData.length === 0) {
    console.log("未找到任何课程包数据");
    process.exit(0);
  }

  console.log(`找到 ${coursePacksData.length} 个课程包，开始导入...`);

  // 处理每个课程包
  for (const coursePackData of coursePacksData) {
    const { meta, courseFiles, dirPath } = coursePackData;

    console.log(`\n开始处理课程包: ${meta.title}`);

    // 创建课程包
    const [coursePackEntity] = await db.insert(coursePack).values(meta).returning();

    console.log(`创建课程包: ${coursePackEntity.title} (ID: ${coursePackEntity.id})`);

    // 处理该课程包下的所有课程文件
    const courseList = await Promise.all(
      courseFiles.map(async (courseFileName, index) => {
        const courseName = path.parse(courseFileName).name;
        const courseTitle = generateCourseTitle(courseName, courseFileName);

        const [course] = await db
          .insert(courseSchema)
          .values({
            coursePackId: coursePackEntity.id,
            // Index starts from 0
            order: index + 1,
            title: courseTitle,
          })
          .returning({ id: courseSchema.id, order: courseSchema.order, title: courseSchema.title });

        console.log(`  创建课程: id-${course.id} order-${course.order} title-${course.title}`);

        return {
          ...course,
          meta: {
            courseFileName,
            courseName,
            dirPath,
          },
        };
      }),
    );

    // 处理每个课程的语句数据
    await Promise.all(
      courseList.map(async (course) => {
        const { id: courseId, meta } = course;

        try {
          const courseDataJsonText = fs.readFileSync(
            path.join(meta.dirPath, meta.courseFileName),
            "utf-8",
          );

          const parsedData = JSON.parse(courseDataJsonText);

          // 检查数据是否为数组
          if (!Array.isArray(parsedData)) {
            console.error(`    错误: ${meta.courseFileName} 不是数组格式，跳过该文件`);
            console.error(`    文件内容类型: ${typeof parsedData}`);
            console.error(`    文件内容: ${JSON.stringify(parsedData).substring(0, 200)}...`);
            return;
          }

          const statementList = parsedData as Statement[];

          let order = 1;
          const statementInsertTask = statementList.map(async (statement) => {
            return await db.insert(statementSchema).values({
              ...statement,
              order: order++,
              courseId,
            });
          });

          console.log(`    课程 ${meta.courseFileName} 开始上传语句 (${statementList.length} 条)`);
          await Promise.all(statementInsertTask);
          console.log(`    课程 ${meta.courseFileName} 语句上传成功`);
        } catch (error) {
          console.error(`    处理课程 ${meta.courseFileName} 时出错:`, error);
        }
      }),
    );

    console.log(`课程包 ${meta.title} 处理完成\n`);
  }

  console.log("全部课程包创建完成");
  process.exit(0);
})();

// 生成课程标题
function generateCourseTitle(courseName: string, courseFileName: string): string {
  // 处理日语课程文件名（如 japanese-01.json）
  if (courseName.startsWith("japanese-")) {
    const num = courseName.replace("japanese-", "");
    return convertToChineseNumber(num);
  }

  // 处理日语单词文件名（如 あいさつ.json）
  if (courseFileName.match(/[\u3040-\u309F\u30A0-\u30FF]/)) {
    // 如果文件名包含日语字符，直接使用文件名（去掉扩展名）
    return courseName;
  }

  // 处理其他数字文件名
  if (/^\d+$/.test(courseName)) {
    return convertToChineseNumber(courseName);
  }

  // 默认返回文件名
  return courseName;
}

function convertToChineseNumber(numStr: string): string {
  const chineseNumbers = ["零", "一", "二", "三", "四", "五", "六", "七", "八", "九", "十"];
  let chineseStr = "第";

  const num = parseInt(numStr);
  if (num >= 10) {
    const tens = Math.floor(num / 10);
    const ones = num % 10;

    if (tens !== 1) {
      chineseStr += chineseNumbers[tens];
    }
    chineseStr += "十";
    if (ones !== 0) {
      chineseStr += chineseNumbers[ones];
    }
  } else {
    chineseStr += chineseNumbers[num];
  }
  chineseStr += "课";
  return chineseStr;
}
